<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shutdown Warning</title>
    <style>
        /* Shutdown Overlay CSS with iPhone-style slide to unlock */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            /* 预设背景色避免白屏 */
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        }

        .overlay-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            /* 确保立即可见 */
            opacity: 1;
            visibility: visible;
        }

        /* Loading 状态样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
            transition: opacity 0.5s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fbbf24;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.1rem;
            opacity: 0.9;
            animation: pulse 2s infinite;
        }

        .background-blur {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
            backdrop-filter: blur(20px);
            z-index: -1;
        }

        .warning-content {
            text-align: center;
            color: white;
            max-width: 950px; /* 调大卡片最大宽度 */
            width: 90%;
            padding: 50px; /* 调大卡片内边距 */
            background: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            animation: slideInUp 0.6s ease-out;
            opacity: 0;
            transform: translateY(50px) scale(0.95);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .warning-content.ready {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 顶部图标和文字排列优化 */
        .top-header {
            display: flex; /* 使用 Flexbox 布局 */
            align-items: center; /* 垂直居中对齐 */
            justify-content: center; /* 水平居中对齐 */
            margin-bottom: 20px; /* 调整与下方内容的间距 */
            gap: 15px; /* 图标和文字之间的间距 */
        }

        .warning-icon {
            color: #fbbf24;
            animation: pulse 2s infinite;
        }

        .warning-icon svg {
            width: 60px; /* 调整图标大小 */
            height: 60px; /* 调整图标大小 */
            filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.3));
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        /* 调整大字警告字体大小 */
        h1 {
            font-size: 1.8rem; /* 调整为更小的字体 */
            font-weight: 300;
            margin: 0; /* 移除默认margin，由top-header的gap控制间距 */
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .warning-message {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            align-items: stretch; /* 确保子项高度一致 */
            justify-content: space-between;
            gap: 40px;
            margin-bottom: 40px;
        }

        .countdown-section {
            flex: 1; /* 让倒计时部分占据可用空间 */
            min-width: 200px; /* 最小宽度，防止过小 */
            display: flex; /* 使用 flexbox 确保内部内容垂直居中 */
            align-items: center;
            justify-content: center;
        }

        .countdown-display {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%; /* 确保填满其父容器 */
        }

        .sentence-section {
            flex: 2; /* 让诗句部分占据更多可用空间 */
            text-align: left;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* 调整倒计时和诗句的字体大小 */
        #countdown-timer {
            font-size: 5.5rem; /* 进一步调大字体 */
            font-weight: 200;
            color: #fbbf24;
            text-shadow: 0 4px 8px rgba(251, 191, 36, 0.3);
            display: block;
            line-height: 1;
        }

        .countdown-label {
            font-size: 1.3rem; /* 进一步调大字体 */
            opacity: 0.8;
            margin-top: 5px;
            display: block;
        }

        .sentence-content {
            font-family: 'LXGWWenKai-Regular', serif;
            font-size: 1.8rem; /* 进一步调大字体 */
            font-weight: bold; /* 加粗诗词文字 */
            line-height: 1.6;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.95);
        }

        .sentence-meta {
            font-size: 1.2rem; /* 进一步调大字体 */
            color: rgba(255, 255, 255, 0.7);
            font-style: italic;
        }

        .slide-to-cancel-container {
            margin: 40px 0;
        }

        .slide-track {
            position: relative;
            width: 100%;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 30px; /* 保持与 slide-button 的圆角一致 */
            border: 2px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            cursor: pointer;
            display: flex; /* 使用Flexbox确保滑块垂直居中 */
            align-items: center; /* 滑块在轨道中垂直居中 */
        }

        .slide-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .slide-text {
            font-size: 1rem;
            font-weight: 500;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            transition: opacity 0.3s ease, color 0.3s ease;
            pointer-events: none;
        }

        .slide-arrow {
            position: absolute;
            right: 20px;
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.6);
            animation: slideHint 2s infinite;
            pointer-events: none;
        }

        @keyframes slideHint {
            0%, 100% {
                transform: translateX(0);
                opacity: 0.6;
            }
            50% {
                transform: translateX(10px);
                opacity: 1;
            }
        }

        .slide-button {
            position: relative; /* 将 absolute 改为 relative，让它在 flex 容器中正常流动 */
            left: 4px;
            /* 移除 top 属性，由 flexbox 自动居中 */
            width: 52px;
            height: 52px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 26px; /* 保持与 slide-track 的圆角一致 */
            cursor: grab;
            transition: box-shadow 0.3s ease, transform 0.1s ease-out; /* 添加 transform 过渡 */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }

        .slide-button:active {
            cursor: grabbing;
        }

        .slide-button-inner {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            border-radius: 26px;
            transition: all 0.3s ease;
        }

        .slide-track.dragging .slide-button {
            transition: none;
        }

        .slide-track.dragging .slide-arrow {
            opacity: 0;
        }

        /* 字体加载 */
        @font-face {
            font-family: 'LXGWWenKai-Regular';
            src: url('LXGWWenKai-Regular.ttf') format('truetype');
            font-display: swap;
        }

        /* Responsive adjustments */
        @media (max-width: 800px) {
            .warning-content {
                padding: 40px; /* 调整内边距 */
            }
            .main-content {
                flex-direction: column;
                gap: 20px;
            }

            .countdown-section {
                min-width: auto;
                align-self: center;
            }

            .sentence-section {
                text-align: center;
            }
            .top-header {
                flex-direction: column; /* 小屏幕下图标和文字改为垂直堆叠 */
                gap: 10px;
            }
            .warning-icon svg {
                width: 50px;
                height: 50px;
            }
            h1 {
                font-size: 1.6rem;
            }
            .sentence-content {
                font-size: 1.6rem; /* 调整手机端诗词字体 */
            }
            .sentence-meta {
                font-size: 1.1rem; /* 调整手机端诗词来源字体 */
            }
        }

        @media (max-width: 600px) {
            .warning-content {
                margin: 20px;
                padding: 30px 20px;
            }

            h1 {
                font-size: 1.4rem; /* 手机端进一步调小 */
            }

            #countdown-timer {
                font-size: 4.5rem; /* 手机端调小一些 */
            }

            .slide-track {
                height: 50px;
            }

            .slide-button {
                width: 42px;
                height: 42px;
                border-radius: 21px;
            }

            .slide-button-inner {
                border-radius: 21px;
            }

            .sentence-content {
                font-size: 1.4rem; /* 手机端再调小一些 */
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .overlay-container {
                background: #000000;
            }

            .warning-content {
                background: rgba(255, 255, 255, 0.95);
                color: #000000;
                border: 2px solid #ffffff;
            }

            .slide-track {
                background: #333333;
                border: 2px solid #ffffff;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .warning-content {
                animation: none;
            }

            .warning-icon {
                animation: none;
            }

            .slide-arrow {
                animation: none;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.8; }
            }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在初始化关机警告...</div>
        </div>
    </div>

    <div class="overlay-container">
        <div class="warning-content" id="warning-content">
            <div class="top-header">
                <div class="warning-icon">
                    <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 8V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 16H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h1>系统即将关机</h1>
            </div>

            <p class="warning-message">您的电脑将在以下时间自动关机：</p>

            <div class="main-content">
                <div class="countdown-section">
                    <div class="countdown-display">
                        <span id="countdown-timer">30</span>
                        <span class="countdown-label">秒</span>
                    </div>
                </div>

                <div class="sentence-section">
                    <div class="sentence-content" id="sentence-content">
                        正在加载诗句...
                    </div>
                    <div class="sentence-meta" id="sentence-meta">
                    </div>
                </div>
            </div>

            <div class="slide-to-cancel-container">
                <div class="slide-track">
                    <div class="slide-background">
                        <span class="slide-text">滑动以取消</span>
                        <div class="slide-arrow">→</div>
                    </div>
                    <div class="slide-button" id="slide-button">
                        <div class="slide-button-inner">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="background-blur"></div>
    </div>

    <script>
        // Electron 渲染进程模块
        const { ipcRenderer } = require('electron');

        document.addEventListener('DOMContentLoaded', () => {
            const loadingOverlay = document.getElementById('loading-overlay');
            const warningContent = document.getElementById('warning-content');
            const countdownTimer = document.getElementById('countdown-timer');
            const slideButton = document.getElementById('slide-button');
            const slideTrack = slideButton.parentElement;
            const slideText = slideTrack.querySelector('.slide-text');
            const slideArrow = slideTrack.querySelector('.slide-arrow');
            const sentenceContent = document.getElementById('sentence-content');
            const sentenceMeta = document.getElementById('sentence-meta');

            let initialX;
            let offsetX = 0;
            let maxOffsetX;
            let countdownInterval;
            let shutdownSeconds = 30; // 默认关机时间，会被主进程传递的值覆盖

            const sentences = [
                {
                    content: "天下无不散之筵席。",
                    meta: "——谚语"
                },
                {
                    content: "人生若只如初见，何事秋风悲画扇。",
                    meta: "——清·纳兰性德《木兰词·拟古决绝词柬友》"
                },
                {
                    content: "行到水穷处，坐看云起时。",
                    meta: "——唐·王维《终南别业》"
                },
                {
                    content: "莫愁前路无知己，天下谁人不识君。",
                    meta: "——唐·高适《别董大》"
                },
                {
                    content: "愿君多采撷，此物最相思。",
                    meta: "——唐·王维《相思》"
                }
            ];

            // 从主进程接收倒计时时间
            ipcRenderer.on('start-countdown', (event, seconds) => {
                shutdownSeconds = seconds;
                countdownTimer.textContent = shutdownSeconds;
                startCountdown();
                loadingOverlay.classList.add('hidden'); // 隐藏加载层
                setTimeout(() => {
                    loadingOverlay.style.display = 'none'; // 彻底移除
                    warningContent.classList.add('ready'); // 显示内容
                }, 500); // 等待过渡动画完成
            });

            // 随机显示一句诗
            function displayRandomSentence() {
                const randomIndex = Math.floor(Math.random() * sentences.length);
                const randomSentence = sentences[randomIndex];
                sentenceContent.textContent = randomSentence.content;
                sentenceMeta.textContent = randomSentence.meta;
            }

            // 启动倒计时
            function startCountdown() {
                countdownInterval = setInterval(() => {
                    shutdownSeconds--;
                    countdownTimer.textContent = shutdownSeconds;
                    if (shutdownSeconds <= 0) {
                        clearInterval(countdownInterval);
                        // 倒计时结束，通知主进程执行关机
                        ipcRenderer.send('execute-shutdown', 0); // 立即关机
                        // 可以在这里显示一个“正在关机”的提示
                        countdownTimer.textContent = '0';
                        slideTrack.style.opacity = '0.5';
                        slideTrack.style.pointerEvents = 'none';
                        slideText.textContent = '正在关机...';
                        slideArrow.style.display = 'none';
                        slideButton.style.display = 'none';
                    }
                }, 1000);
            }

            // 滑块按下事件 (鼠标和触摸)
            slideButton.addEventListener('mousedown', (e) => {
                e.preventDefault();
                slideTrack.classList.add('dragging');
                initialX = e.clientX - offsetX;
                maxOffsetX = slideTrack.offsetWidth - slideButton.offsetWidth - 8;
                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            });

            slideButton.addEventListener('touchstart', (e) => {
                e.preventDefault();
                slideTrack.classList.add('dragging');
                initialX = e.touches[0].clientX - offsetX;
                maxOffsetX = slideTrack.offsetWidth - slideButton.offsetWidth - 8;
                document.addEventListener('touchmove', onTouchMove);
                document.addEventListener('touchend', onTouchEnd);
            });

            // 滑块移动事件 (鼠标和触摸)
            function onMouseMove(e) {
                let currentX = e.clientX - initialX;
                offsetX = Math.max(0, Math.min(currentX, maxOffsetX));
                slideButton.style.left = `${offsetX + 4}px`;

                const threshold = maxOffsetX * 0.7;
                if (offsetX > threshold) {
                    slideTrack.style.backgroundColor = 'rgba(255, 99, 71, 0.3)';
                    slideText.textContent = '松开以取消';
                } else {
                    slideTrack.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    slideText.textContent = '滑动以取消';
                }
            }

            function onTouchMove(e) {
                let currentX = e.touches[0].clientX - initialX;
                offsetX = Math.max(0, Math.min(currentX, maxOffsetX));
                slideButton.style.left = `${offsetX + 4}px`;

                const threshold = maxOffsetX * 0.7;
                if (offsetX > threshold) {
                    slideTrack.style.backgroundColor = 'rgba(255, 99, 71, 0.3)';
                    slideText.textContent = '松开以取消';
                } else {
                    slideTrack.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    slideText.textContent = '滑动以取消';
                }
            }

            // 滑块松开事件 (鼠标和触摸)
            function onMouseUp() {
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
                slideTrack.classList.remove('dragging');

                if (offsetX >= maxOffsetX * 0.9) {
                    ipcRenderer.send('cancel-shutdown');
                    clearInterval(countdownInterval);
                    slideText.textContent = '关机已取消';
                    slideTrack.style.backgroundColor = 'rgba(144, 238, 144, 0.3)';
                    slideButton.style.left = `${maxOffsetX + 4}px`;
                    slideButton.style.pointerEvents = 'none';
                    slideArrow.style.display = 'none';
                    // 可以在这里延迟关闭窗口或显示确认信息，或等待主进程通知关闭
                } else {
                    offsetX = 0;
                    slideButton.style.left = `4px`;
                    slideTrack.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    slideText.textContent = '滑动以取消';
                }
                slideButton.style.transition = 'left 0.3s ease-out';
                slideButton.style.left = `4px`;
                offsetX = 0;
                setTimeout(() => {
                    slideButton.style.transition = 'none';
                }, 300);
            }

            function onTouchEnd() {
                document.removeEventListener('touchmove', onTouchMove);
                document.removeEventListener('touchend', onTouchEnd);
                slideTrack.classList.remove('dragging');

                if (offsetX >= maxOffsetX * 0.9) {
                    ipcRenderer.send('cancel-shutdown');
                    clearInterval(countdownInterval);
                    slideText.textContent = '关机已取消';
                    slideTrack.style.backgroundColor = 'rgba(144, 238, 144, 0.3)';
                    slideButton.style.left = `${maxOffsetX + 4}px`;
                    slideButton.style.pointerEvents = 'none';
                    slideArrow.style.display = 'none';
                    // 可以在这里延迟关闭窗口或显示确认信息，或等待主进程通知关闭
                } else {
                    offsetX = 0;
                    slideButton.style.left = `4px`;
                    slideTrack.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    slideText.textContent = '滑动以取消';
                }
                slideButton.style.transition = 'left 0.3s ease-out';
                slideButton.style.left = `4px`;
                offsetX = 0;
                setTimeout(() => {
                    slideButton.style.transition = 'none';
                }, 300);
            }

            // 监听取消关机结果
            ipcRenderer.on('cancel-result', (event, result) => {
                if (result.success) {
                    console.log(result.message);
                    // 关机已取消，主进程会关闭窗口，这里不需要额外操作
                } else {
                    console.error(`取消关机失败: ${result.message}`);
                    slideText.textContent = `取消失败: ${result.message}`;
                    slideTrack.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
                    setTimeout(() => {
                        slideText.textContent = '滑动以取消';
                        slideTrack.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    }, 3000);
                }
            });

            // 监听关机执行结果 (可选，用于调试或显示状态)
            ipcRenderer.on('shutdown-result', (event, result) => {
                if (result.success) {
                    console.log(result.message);
                } else {
                    console.error(`关机命令失败: ${result.message}`);
                }
            });

            // 初始化加载诗句
            displayRandomSentence();

            // 注意：这个 HTML 文件需要被 Electron 主进程加载。
            // 例如在 main.js 中:
            // win.loadFile('your_file_name.html');
            //
            // 如果你需要从其他渲染进程触发这个警告，可以通过 IPC 发送消息：
            // ipcRenderer.send('show-shutdown-warning', 60); // 60秒后关机
            // 这段代码假设主进程会接收 'show-shutdown-warning' 并将秒数传递回来
        });
    </script>
</body>
</html>