{"name": "my-electron-app", "private": true, "version": "0.0.0", "main": "electron.main.cjs", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron:dev": "concurrently \"vite\" \"wait-on http://localhost:5173 && electron .\""}, "dependencies": {"naive-ui": "^2.42.0", "unocss": "^66.2.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "concurrently": "^9.2.0", "electron": "^36.5.0", "vite": "^6.3.5", "wait-on": "^8.0.3"}}