const { getSettings } = require('./settings');
let lastTrigger = null;

function scheduler(onTrigger) {
  const arr = getSettings();
  const now = new Date();
  const nowH = now.getHours(), nowM = now.getMinutes(), nowS = now.getSeconds();
  const nowDay = now.getDay();
  for (const item of arr) {
    if (!item.days || !Array.isArray(item.days)) continue;
    if (!item.days.includes(nowDay)) continue;
    if (item.hh === nowH && item.mm === nowM && item.ss === nowS) {
      const key = `${nowDay}-${nowH}-${nowM}-${nowS}`;
      if (lastTrigger !== key) {
        lastTrigger = key;
        onTrigger(item);
      }
      break;
    }
  }
}

module.exports = { scheduler };
