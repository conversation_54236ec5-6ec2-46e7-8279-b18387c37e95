const fs = require('fs');
const path = require('path');
const settingsPath = path.join(__dirname, '../shutdown-settings.json');

function getSettings() {
  if (!fs.existsSync(settingsPath)) {
    fs.writeFileSync(settingsPath, '[]', 'utf-8');
  }
  const data = fs.readFileSync(settingsPath, 'utf-8');
  let arr = [];
  try {
    arr = JSON.parse(data);
  } catch {
    arr = [];
  }
  if (!Array.isArray(arr)) arr = [];
  arr = arr.map(item => ({
    hh: typeof item.hh === 'number' ? item.hh : 0,
    mm: typeof item.mm === 'number' ? item.mm : 0,
    ss: typeof item.ss === 'number' ? item.ss : 0,
    days: Array.isArray(item.days) ? item.days.filter(d => typeof d === 'number') : []
  }));
  return arr;
}

function saveSettings(settings) {
  const formatted = settings.map(item => ({
    hh: typeof item.hh === 'number' ? item.hh : 0,
    mm: typeof item.mm === 'number' ? item.mm : 0,
    ss: typeof item.ss === 'number' ? item.ss : 0,
    days: Array.isArray(item.days) ? item.days.filter(d => typeof d === 'number') : []
  }));
  fs.writeFileSync(settingsPath, JSON.stringify(formatted, null, 2), 'utf-8');
}

module.exports = { getSettings, saveSettings };
