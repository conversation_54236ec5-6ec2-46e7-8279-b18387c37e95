<template>
  <div class="settings flex flex-col items-center justify-center min-h-screen bg-gray-100 p-2 sm:p-4">
    <div class="w-full max-w-lg bg-white rounded-lg shadow-lg p-6 sm:p-8">
      <h1 class="text-xl sm:text-2xl font-bold text-center mb-4 sm:mb-6">自动关机时间设置</h1>
      <n-form label-placement="top" :model="shutdownTimes">
        <n-dynamic-input
          v-model:value="shutdownTimes"
          :on-create="() => ({ time: null, days: [] })"
          #default="{ value, index }"
        >
          <div class="flex flex-col sm:flex-row gap-2 sm:gap-3 items-center w-full mb-2">
            <n-time-picker
              v-model:value="value.time"
              format="HH:mm:ss"
              placeholder="选择时间"
              class="flex-1 w-full sm:w-auto"
              type="time"
              size="medium" />
            <n-select
              v-model:value="value.days"
              multiple
              :options="weekOptions"
              placeholder="选择周几"
              class="flex-1 w-full sm:w-auto"
              size="medium" />
            <n-button type="error" size="medium" @click="removeTime(index)" class="w-full sm:w-auto mt-2 sm:mt-0">删除</n-button>
          </div>
        </n-dynamic-input>
      </n-form>
      <div class="flex flex-col sm:flex-row gap-3 mt-6 justify-center">
        <n-button type="primary" @click="saveSettings" :loading="loading" size="large" class="w-full sm:w-auto">保存设置</n-button>
        <n-button @click="loadSettings" :disabled="loading" size="large" class="w-full sm:w-auto">重载设置</n-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { NButton, NTimePicker, NForm, NDynamicInput, NSelect } from 'naive-ui'

const shutdownTimes = ref([])
const loading = ref(false)
const weekOptions = [
  { label: '周日', value: 0 },
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
]

function removeTime(index) {
  shutdownTimes.value.splice(index, 1)
}

function hmsToTimestamp({ hh, mm, ss }) {
  const now = new Date();
  now.setHours(hh);
  now.setMinutes(mm);
  now.setSeconds(ss);
  now.setMilliseconds(0);
  return now.getTime();
}

function timestampToHms(timestamp) {
  if (typeof timestamp !== 'number' || isNaN(timestamp)) {
    return { hh: 0, mm: 0, ss: 0 };
  }
  const date = new Date(timestamp);
  return {
    hh: date.getHours(),
    mm: date.getMinutes(),
    ss: date.getSeconds()
  };
}

async function loadSettings() {
  loading.value = true
  try {
    const settings = await window.shutdownAPI?.getSettings?.()
    shutdownTimes.value = Array.isArray(settings)
      ? settings.map(item => ({
          time: hmsToTimestamp(item),
          days: Array.isArray(item.days) ? item.days : []
        }))
      : []
  } catch (e) {
    console.error('加载设置失败:', e)
    shutdownTimes.value = []
  } finally {
    loading.value = false
  }
}

async function saveSettings() {
  loading.value = true
  try {
    const plain = shutdownTimes.value.map(item => {
      const { hh, mm, ss } = timestampToHms(item.time);
      return {
        hh, mm, ss,
        days: Array.isArray(item.days) ? [...item.days] : []
      }
    }).filter(item => item.days.length > 0);

    await window.shutdownAPI?.setSettings?.(plain)
  } catch (e) {
    console.error('保存设置失败:', e)
  } finally {
    loading.value = false
  }
}

onMounted(loadSettings)
</script>

<style scoped>
body {
  background: #f5f6fa;
}
</style>
