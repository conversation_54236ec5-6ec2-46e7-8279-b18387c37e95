<template>
  <div class="loading-overlay" v-if="isLoading" fixed inset-0 bg-gradient-to-br from-[#1a1a2e] to-[#0f3460] flex items-center justify-center z-10001 transition-opacity duration-500>
    <div class="loading-content text-center text-white">
      <div class="loading-spinner w-50px h-50px border-3 border-[rgba(255,255,255,0.3)] border-t-[#fbbf24] rounded-full animate-spin mb-10px"></div>
      <div class="loading-text text-1rem">正在初始化...</div>
    </div>
  </div>

  <div class="overlay-container fixed inset-0 bg-gradient-to-br from-[#1a1a2e] to-[#0f3460] flex items-center justify-center z-10000">
    <div class="warning-content max-w-800px w-90% p-30px bg-[rgba(255,255,255,0.1)] rounded-16px border border-[rgba(255,255,255,0.2)] text-white transition-opacity duration-500" :class="{ 'opacity-100': isReady, 'opacity-0': !isReady }">
      <div class="top-header flex items-center justify-center mb-15px gap-10px">
        <div class="warning-icon text-[#fbbf24]">
          <svg w-50px h-50px viewBox="0 0 24 24" fill="none">
            <path d="M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 8V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 16H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 text-1.6rem font-300>系统即将关闭</h1>
      </div>

      <p class="warning-message text-1rem mb-20px text-center">您的电脑将在以下时间自动关闭：</p>

      <div class="main-content flex items-stretch gap-20px mb-20px">
        <div class="countdown-section flex-1 min-w-150px flex items-center justify-center">
          <div class="countdown-display w-full p-15px bg-[rgba(255,255,255,0.1)] rounded-12px border border-[rgba(255,255,255,0.2)] text-center">
            <span class="text-display text-4rem font-200 text-[#fbbf24] block">{{ displayTime }}</span>
            <span class="countdown-label text-1rem opacity-80 block">秒</span>
          </div>
        </div>

        <div class="sentence-section flex-2 p-15px bg-[rgba(255,255,255,0.05)] rounded-12px border border-[rgba(255,255,255,0.1)] flex flex-col justify-center">
          <div class="sentence-content text-1.4rem font-bold mb-10px">{{ sentence }}</div>
          <div class="sentence-meta text-1rem text-[rgba(255,255,255,0.7)] italic">{{ sentenceMeta }}</div>
        </div>
      </div>

      <div class="slide-to-cancel-container">
        <div class="slide-track relative w-full h-50px bg-[rgba(255,255,255,0.1)] rounded-25px border border-[rgba(255,255,255,0.2)] flex items-center cursor-pointer" @mousedown="startDragging" @touchstart="startDragging" @mousemove="handleDragging" @touchmove="handleDragging" @mouseup="endDragging" @touchend="endDragging">
          <div class="slide-background absolute inset-0 flex items-center justify-center">
            <span class="slide-text text-1rem text-white select-none">滑动以取消</span>
            <div class="slide-arrow absolute right-15px text-1rem text-[rgba(255,255,255,0.6)] select-none">→</div>
          </div>
          <div class="slide-button relative left-2px w-46px h-46px bg-gradient-to-br from-[#ffffff] to-[#f8f9fa] rounded-full cursor-grab transition-transform duration-200" :style="{ transform: `translateX(${slidePos}px)` }">
            <div class="slide-button-inner w-full h-full flex items-center justify-center text-[#6b7280] rounded-full">
              <svg w-20px h-20px viewBox="0 0 24 24" fill="none">
                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import poemData from '../assets/sentences.json'

const time = ref(30)
const displayTime = ref('')
const isLoading = ref(true)
const isReady = ref(false)
const slidePos = ref(0)
const isDragging = ref(false)
const startX = ref(0)
const trackWidth = ref(0)
const sentence = ref('正在加载诗句...')
const sentenceMeta = ref('')
let timer = null

function formatTime(sec) {
  return String(sec).padStart(2, '0')
}

function shutdown() {
  window.electron?.ipcRenderer?.send('shutdown')
}

function loadRandomQuote() {
  if (!poemData || poemData.length === 0) {
    sentence.value = '未找到诗句'
    sentenceMeta.value = ''
    return
  }
  const randomPoem = poemData[Math.floor(Math.random() * poemData.length)]
  const randomContent = randomPoem.content[Math.floor(Math.random() * randomPoem.content.length)]
  sentence.value = randomContent.replace(/\(.*?\)/, '').trim()
  sentenceMeta.value = `${randomPoem.title} - ${randomPoem.author}`
}

function startDragging(e) {
  isDragging.value = true
  startX.value = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX
  const track = e.currentTarget
  trackWidth.value = track.offsetWidth - 48
}

function handleDragging(e) {
  if (!isDragging.value) return
  const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX
  const deltaX = clientX - startX.value
  slidePos.value = Math.max(0, Math.min(deltaX, trackWidth.value))
  if (slidePos.value >= trackWidth.value * 0.9) {
    cancelShutdown()
  }
}

function endDragging() {
  if (slidePos.value < trackWidth.value * 0.9) {
    slidePos.value = 0
  }
  isDragging.value = false
}

function cancelShutdown() {
  clearInterval(timer)
  time.value = 0
  displayTime.value = '已取消'
}

onMounted(() => {
  loadRandomQuote()
  setTimeout(() => {
    isLoading.value = false
    setTimeout(() => isReady.value = true, 100)
  }, 1000)

  displayTime.value = formatTime(time.value)
  timer = setInterval(() => {
    if (time.value > 0) {
      time.value--
      displayTime.value = formatTime(time.value)
      if (time.value === 0) {
        shutdown()
      }
    }
  }, 1000)
})

onUnmounted(() => {
  if (timer) clearInterval(timer)
})
</script>

<style scoped>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  margin: 0;
  overflow: hidden;
  user-select: none;
}

.sentence-content {
  font-family: 'LXGWWenKai-Regular', serif;
}
</style>