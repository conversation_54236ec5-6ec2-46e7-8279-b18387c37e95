// main.js
const { app, BrowserWindow, Tray, Menu, ipcMain } = require('electron');
const path = require('path');
const { exec } = require('child_process');
const fs = require('fs');

let mainWindow;
let tray;
let isQuiting = false; // 标记是否真正退出
let countdownWindow = null;
let checkTimer = null;

// 引入拆分后的模块
const { getSettings, saveSettings } = require('./src/settings');
const { scheduler } = require('./src/scheduler');
const { showCountdown } = require('./src/countdown');

// 创建主窗口
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      contextIsolation: true, // 启用上下文隔离
      nodeIntegration: false, // 禁用 Node.js 集成
      preload: path.join(__dirname, 'preload.js'), // 预加载脚本
    },
  });
  // 监听关闭事件，隐藏窗口而不是退出
  mainWindow.on('close', (event) => {
    if (!isQuiting) {
      event.preventDefault();
      mainWindow.hide();
    }
  });
  // 根据是否打包加载不同的页面
  if (app.isPackaged) {
    mainWindow.loadFile(path.join(__dirname, 'shutdown.html'));
  } else {
    mainWindow.loadFile(path.join(__dirname, 'shutdown.html'));
  }
}

// 创建系统托盘
function createTray() {
  tray = new Tray(path.join(__dirname, 'nezha.jpg'));
  const contextMenu = Menu.buildFromTemplate([
    { label: '显示主窗口', click: () => mainWindow.show() },
    { label: '显示倒计时', click: () => createCountdownWindow(60) },
    { type: 'separator' },
    { label: '退出', click: () => {
        isQuiting = true;
        app.quit();
      }
    },
  ]);
  tray.setToolTip('自动关机应用');
  tray.setContextMenu(contextMenu);
  tray.on('double-click', () => mainWindow.show());
}

function createCountdownWindow(seconds = 60) {
  if (countdownWindow && !countdownWindow.isDestroyed()) {
    countdownWindow.show();
    countdownWindow.focus();
    countdownWindow.webContents.send('countdown', seconds);
    return;
  }
  countdownWindow = new BrowserWindow({
    fullscreen: true,
    alwaysOnTop: true,
    frame: false,
    transparent: false,
    webPreferences: {
      contextIsolation: true,
      nodeIntegration: false,
      preload: path.join(__dirname, 'preload.js'),
    },
  });
  countdownWindow.on('close', (event) => {
    event.preventDefault();
    countdownWindow.hide();
  });
  if (app.isPackaged) {
    countdownWindow.loadFile(path.join(__dirname, 'dist/index.html#\/countdown'));
  } else {
    countdownWindow.loadURL('http://localhost:5173/countdown');
  }
  countdownWindow.webContents.on('did-finish-load', () => {
    countdownWindow.webContents.send('countdown', seconds);
  });
}

ipcMain.on('show-countdown-window', (_event, seconds) => {
  createCountdownWindow(seconds);
});

// 应用准备就绪
app.whenReady().then(() => {
  createMainWindow();
  createTray();
  // 每秒调度
  checkTimer = setInterval(() => {
    scheduler((item) => createCountdownWindow(60));
  }, 1000);
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  // 不退出，保持后台运行
  // if (process.platform !== 'darwin') {
  //   app.quit();
  // }
});

// IPC 处理：读取设置
ipcMain.handle('get-shutdown-settings', async () => getSettings());

// IPC 处理：保存设置
ipcMain.handle('set-shutdown-settings', async (event, settings) => {
  try {
    saveSettings(settings);
    return { success: true };
  } catch (e) {
    return { success: false, error: e.message };
  }
});

// IPC 处理：关机
ipcMain.on('shutdown', () => {
  exec('shutdown /s /t 60');
});