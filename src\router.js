import { createRouter, createWebHashHistory } from 'vue-router'
import Home from './views/Home.vue'
import CountdownPage from './views/CountdownPage.vue'
import Settings from './views/Settings.vue'

const routes = [
  { path: '/', component: Home },
  { path: '/countdown', component: CountdownPage },
  { path: '/settings', component: Settings }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
